
import type { Dhikr } from '@/types/dhikr';

export const ALL_DHIKR: Dhikr[] = [
  {
    id: 'subhanallah-wabihamdihi-adanakhalkih',
    arabicText: 'سُبْحَانَ اللهِ وَبِحَمْدِهِ عَدَدَ خَلْقِهِ، وَرِضَا نَفْسِهِ، وَزِنَةَ عَرْشِهِ، وَمِدَادَ كَلِمَاتِهِ',
    translation_en: 'Glory is to Allah and praise is to Him, by the multitude of His creation, by His Pleasure, by the weight of His Throne, and by the extent of His Words.',
    category: 'الصباح',
    recommendedCount: 3,
    virtue_ar: 'يقال ثلاث مرات في الصباح.',
    audioSrc: '/audio/subhanallah_wabihamdihi_adada_khalqihi.mp3',
  },
  {
    id: 'allahumma-bika-asbahna',
    arabicText: 'اللَّهُمَّ بِكَ أَصْبَحْنَا، وَبِكَ أَمْسَيْنَا، وَبِكَ نَحْيَا، وَبِكَ نَمُوتُ، وَإِلَيْكَ النُّشُورُ',
    translation_en: 'O <PERSON>, by You we have reached the morning and by You we have reached the evening, by You we live and by You we die, and to You is the resurrection.',
    category: 'الصباح',
    recommendedCount: 1,
    audioSrc: '/audio/allahumma_bika_asbahna.mp3',
  },
  {
    id: 'allahumma-bika-amsayna',
    arabicText: 'اللَّهُمَّ بِكَ أَمْسَيْنَا، وَبِكَ أَصْبَحْنَا، وَبِكَ نَحْيَا، وَبِكَ نَمُوتُ، وَإِلَيْكَ الْمَصِيرُ',
    translation_en: 'O Allah, by You we have reached the evening and by You we have reached the morning, by You we live and by You we die, and to You is the return.',
    category: 'المساء',
    recommendedCount: 1,
    audioSrc: '/audio/allahumma_bika_amsayna.mp3',
  },
  {
    id: 'sayyidul-istighfar',
    arabicText: 'اللَّهُمَّ أَنْتَ رَبِّي لَا إِلَهَ إِلَّا أَنْتَ، خَلَقْتَنِي وَأَنَا عَبْدُكَ، وَأَنَا عَلَى عَهْدِكَ وَوَعْدِكَ مَا اسْتَطَعْتُ، أَعُوذُ بِكَ مِنْ شَرِّ مَا صَنَعْتُ، أَبُوءُ لَكَ بِنِعْمَتِكَ عَلَيَّ، وَأَبُوءُ لَكَ بِذَنْبِي فَاغْفِرْ لِي، فَإِنَّهُ لَا يَغْفِرُ الذُّنُوبَ إِلَّا أَنْتَ',
    translation_en: 'O Allah, You are my Lord, none has the right to be worshipped except You. You created me and I am Your servant, and I abide by Your covenant and promise as best I can. I seek refuge in You from the evil of what I have committed. I acknowledge Your favor upon me and I acknowledge my sin, so forgive me, for verily none can forgive sins except You.',
    category: 'الصباح', // Also for evening
    recommendedCount: 1,
    virtue_ar: 'من قالها موقنا بها حين يمسي فمات من ليلته دخل الجنة، وكذلك إذا أصبح.',
    audioSrc: '/audio/sayyidul_istighfar.mp3',
  },
  {
    id: 'ayat-al-kursi',
    arabicText: 'اللَّهُ لَا إِلَهَ إِلَّا هُوَ الْحَيُّ الْقَيُّومُ لَا تَأْخُذُهُ سِنَةٌ وَلَا نَوْمٌ لَهُ مَا فِي السَّمَاوَاتِ وَمَا فِي الْأَرْضِ مَنْ ذَا الَّذِي يَشْفَعُ عِنْدَهُ إِلَّا بِإِذْنِهِ يَعْلَمُ مَا بَيْنَ أَيْدِيهِمْ وَمَا خَلْفَهُمْ وَلَا يُحِيطُونَ بِشَيْءٍ مِنْ عِلْمِهِ إِلَّا بِِمَا شَاءَ وَسِعَ كُرْسِيُّهُ السَّمَاوَاتِ وَالْأَرْضَ وَلَا يَئُودُهُ حِفْظُهُمَا وَهُوَ الْعَلِيُّ الْعَظِيمُ',
    translation_en: 'Allah! There is no god but He - the Living, The Self-subsisting, Eternal. No slumber can seize Him Nor Sleep. His are all things In the heavens and on earth. Who is there can intercede In His presence except As he permitteth? He knoweth What (appeareth to His creatures As) Before or After or Behind them. Nor shall they compass Aught of his knowledge Except as He willeth. His throne doth extend Over the heavens And on earth, and He feeleth No fatigue in guarding And preserving them, For He is the Most High, The Supreme (in glory).',
    category: 'بعد الصلاة', // Also morning/evening
    recommendedCount: 1,
    virtue_ar: 'من قرأها دبر كل صلاة لم يمنعه من دخول الجنة إلا أن يموت.',
    audioSrc: '/audio/ayat_al_kursi.mp3',
  },
  {
    id: 'subhanallah-33',
    arabicText: 'سُبْحَانَ اللَّهِ',
    translation_en: 'Glory is to Allah',
    category: 'بعد الصلاة',
    recommendedCount: 33,
    audioSrc: '/audio/subhanallah.mp3',
  },
  {
    id: 'alhamdulillah-33',
    arabicText: 'الْحَمْدُ لِلَّهِ',
    translation_en: 'Praise is to Allah',
    category: 'بعد الصلاة',
    recommendedCount: 33,
    audioSrc: '/audio/alhamdulillah.mp3',
  },
  {
    id: 'allahuakbar-33',
    arabicText: 'اللَّهُ أَكْبَرُ',
    translation_en: 'Allah is the Greatest',
    category: 'بعد الصلاة',
    recommendedCount: 33,
    audioSrc: '/audio/allahu_akbar.mp3',
  },
  {
    id: 'la-ilaha-illa-allah-wahdahu',
    arabicText: 'لَا إِلَهَ إِلَّا اللَّهُ وَحْدَهُ لَا شَرِيكَ لَهُ، لَهُ الْمُلْكُ وَلَهُ الْحَمْدُ وَهُوَ عَلَى كُلِّ شَيْءٍ قَدِيرٌ',
    translation_en: 'None has the right to be worshipped but Allah alone, Who has no partner. His is the dominion and His is the praise and He is Able to do all things.',
    category: 'عام',
    recommendedCount: 10, // Often 10 or 100 times
    virtue_ar: 'من قالها مائة مرة كانت له عدل عشر رقاب، وكتبت له مائة حسنة، ومحيت عنه مائة سيئة، وكانت له حرزاً من الشيطان يومه ذلك حتى يمسي، ولم يأت أحد بأفضل مما جاء به إلا أحد عمل أكثر من ذلك.',
    audioSrc: '/audio/la_ilaha_illa_allah_wahdahu.mp3',
  },
  {
    id: 'allahumma-ajirni-minan-nar',
    arabicText: 'اللَّهُمَّ أَجِرْنِي مِنَ النَّارِ',
    translation_en: 'O Allah, protect me from the Fire.',
    category: 'المساء', // Usually after Fajr and Maghrib prayers
    recommendedCount: 7,
    virtue_ar: 'من قالها سبع مرات بعد صلاتي الفجر والمغرب، فإن مات من يومه أو ليلته، كتب الله له جواراً من النار.',
    audioSrc: '/audio/allahumma_ajirni_minan_nar.mp3',
  },
  {
    id: 'astaghfirullah-al-azim',
    arabicText: 'أَسْتَغْفِرُ اللَّهَ الْعَظِيمَ الَّذِي لَا إِلَهَ إِلَّا هُوَ الْحَيَّ الْقَيُّومَ وَأَتُوبُ إِلَيْهِ',
    translation_en: 'I seek the forgiveness of Allah the Mighty, Whom there is none worthy of worship except Him, the Living, the Eternal, and I repent unto Him.',
    category: 'عام',
    recommendedCount: 3, // Or more
    virtue_ar: 'من قاله غفرت ذنوبه وإن كان قد فر من الزحف.',
    audioSrc: '/audio/astaghfirullah_al_azim.mp3',
  },
  {
    id: 'raditu-billahi-rabban',
    arabicText: 'رَضِيتُ بِاللَّهِ رَبًّا، وَبِالْإِسْلَامِ دِينًا، وَبِمُحَمَّدٍ صلى الله عليه وسلم نَبِيًّا',
    translation_en: 'I am pleased with Allah as my Lord, with Islam as my religion, and with Muhammad (peace and blessings of Allah be upon him) as my Prophet.',
    category: 'الصباح',
    recommendedCount: 3,
    virtue_ar: 'من قالها ثلاثاً حين يصبح وحين يمسي كان حقاً على الله أن يرضيه يوم القيامة.',
    audioSrc: '/audio/raditu_billahi_rabban.mp3',
  },
   {
    id: 'bismillahilladhi-la-yadurru',
    arabicText: 'بِسْمِ اللَّهِ الَّذِي لَا يَضُرُّ مَعَ اسْمِهِ شَيْءٌ فِي الْأَرْضِ وَلَا فِي السَّمَاءِ وَهُوَ السَّمِيعُ الْعَلِيمُ',
    translation_en: 'In the Name of Allah, with Whose Name nothing on earth or in heaven can cause harm, and He is the All-Hearing, the All-Knowing.',
    category: 'الصباح', // Also for evening
    recommendedCount: 3,
    virtue_ar: 'من قالها ثلاثاً حين يصبح لم يصبه فجأة بلاء حتى يمسي، ومن قالها حين يمسي لم يصبه فجأة بلاء حتى يصبح.',
    audioSrc: '/audio/bismillahilladhi_la_yadurru.mp3',
  },
  {
    id: 'hasbiyallahu-la-ilaha-illa-huwa',
    arabicText: 'حَسْبِيَ اللَّهُ لَا إِلَهَ إِلَّا هُوَ عَلَيْهِ تَوَكَّلْتُ وَهُوَ رَبُّ الْعَرْشِ الْعَظِيمِ',
    translation_en: 'Allah is sufficient for me. There is none worthy of worship but Him. I have placed my trust in Him, He is Lord of the Majestic Throne.',
    category: 'عام', // Often recited morning and evening
    recommendedCount: 7,
    virtue_ar: 'من قالها حين يصبح وحين يمسي سبع مرات كفاه الله ما أهمه من أمر الدنيا والآخرة.',
    audioSrc: '/audio/hasbiyallahu_la_ilaha_illa_huwa.mp3',
  },
  {
    id: 'audhu-bikalimatillahit-tammati',
    arabicText: 'أَعُوذُ بِكَلِمَاتِ اللَّهِ التَّامَّاتِ مِنْ شَرِّ مَا خَلَقَ',
    translation_en: 'I seek refuge in the Perfect Words of Allah from the evil of what He has created.',
    category: 'المساء',
    recommendedCount: 3,
    virtue_ar: 'من قالها حين يمسي ثلاث مرات لم يضره حمة تلك الليلة.',
    audioSrc: '/audio/audhu_bikalimatillahit_tammati.mp3',
  },
  {
    id: 'bismika-rabbi-wadatu-janbi',
    arabicText: 'بِاسْمِكَ رَبِّ وَضَعْتُ جَنْبِي، وَبِكَ أَرْفَعُهُ، إِنْ أَمْسَكْتَ نَفْسِي فَارْحَمْهَا، وَإِنْ أَرْسَلْتَهَا فَاحْفَظْهَا بِمَا تَحْفَظُ بِهِ عِبَادَكَ الصَّالِحِينَ',
    translation_en: 'In Your Name my Lord, I lie down and in Your Name I rise. If You take my soul, have mercy on it, and if You return it, protect it as You protect Your righteous slaves.',
    category: 'قبل النوم',
    recommendedCount: 1,
    audioSrc: '/audio/bismika_rabbi_wadatu_janbi.mp3',
  },
  {
    id: 'alhamdulillahilladhi-ahyana',
    arabicText: 'الْحَمْدُ لِلَّهِ الَّذِي أَحْيَانَا بَعْدَ مَا أَمَاتَنَا وَإِلَيْهِ النُّشُورُ',
    translation_en: 'All praise is for Allah who gave us life after having taken it from us and unto Him is the resurrection.',
    category: 'عند الاستيقاظ',
    recommendedCount: 1,
    audioSrc: '/audio/alhamdulillahilladhi_ahyana.mp3',
  },
  {
    id: 'allahumma-aftahli-abwaba-rahmatik',
    arabicText: 'اللَّهُمَّ افْتَحْ لِي أَبْوَابَ رَحْمَتِكَ',
    translation_en: 'O Allah, open the gates of Your mercy for me.',
    category: 'دخول المسجد',
    recommendedCount: 1,
    audioSrc: '/audio/allahumma_aftahli_abwaba_rahmatik.mp3',
  },
  {
    id: 'allahumma-inni-asaluka-min-fadlik',
    arabicText: 'اللَّهُمَّ إِنِّي أَسْأَلُكَ مِنْ فَضْلِكَ',
    translation_en: 'O Allah, I ask You from Your bounty.',
    category: 'الخروج من المسجد',
    recommendedCount: 1,
    audioSrc: '/audio/allahumma_inni_asaluka_min_fadlik.mp3',
  },
  {
    id: 'astaghfirullaha-wa-atubu-ilaih-100',
    arabicText: 'أَسْتَغْفِرُ اللَّهَ وَأَتُوبُ إِلَيْهِ',
    translation_en: 'I seek the forgiveness of Allah and repent to Him.',
    category: 'عام',
    recommendedCount: 100,
    virtue_ar: 'من لزم الاستغفار جعل الله له من كل هم فرجاً، ومن كل ضيق مخرجاً، ورزقه من حيث لا يحتسب.',
    audioSrc: '/audio/astaghfirullaha_wa_atubu_ilaih.mp3',
  },
  {
    id: 'allahumma-salli-ala-muhammad-10',
    arabicText: 'اللَّهُمَّ صَلِّ وَسَلِّمْ عَلَى نَبِيِّنَا مُحَمَّدٍ',
    translation_en: 'O Allah, send Your peace and blessings upon our Prophet Muhammad.',
    category: 'الصباح', // Also evening
    recommendedCount: 10,
    virtue_ar: 'من صلى علي حين يصبح عشراً وحين يمسي عشراً أدركته شفاعتي يوم القيامة.',
    audioSrc: '/audio/allahumma_salli_ala_muhammad.mp3',
  },
  {
    id: 'dua-after-adhan',
    arabicText: 'اللَّهُمَّ رَبَّ هَذِهِ الدَّعْوَةِ التَّامَّةِ، وَالصَّلَاةِ الْقَائِمَةِ، آتِ مُحَمَّدًا الْوَسِيلَةَ وَالْفَضِيلَةَ، وَابْعَثْهُ مَقَامًا مَحْمُودًا الَّذِي وَعَدْتَهُ',
    translation_en: 'O Allah, Lord of this perfect call and established prayer, grant Muhammad the intercession and favor, and raise him to the praiseworthy station You have promised him.',
    category: 'بعد الأذان',
    recommendedCount: 1,
    virtue_ar: 'من قال حين يسمع النداء: ... حلت له شفاعتي يوم القيامة.',
    audioSrc: '/audio/dua_after_adhan.mp3',
  },
  {
    id: 'la-ilaha-illa-anta-subhanaka',
    arabicText: 'لَا إِلَهَ إِلَّا أَنْتَ سُبْحَانَكَ إِنِّي كُنْتُ مِنَ الظَّالِمِينَ',
    translation_en: 'There is no deity except You; exalted are You. Indeed, I have been of the wrongdoers.',
    category: 'عند الكرب',
    recommendedCount: 1, // Or more as needed
    virtue_ar: 'دعاء ذي النون إذ دعا وهو في بطن الحوت، لم يدع بها رجل مسلم في شيء قط إلا استجاب الله له.',
    audioSrc: '/audio/la_ilaha_illa_anta_subhanaka.mp3',
  },
  {
    id: 'allahumma-infani-bima-allamtani',
    arabicText: 'اللَّهُمَّ انْفَعْنِي بِمَا عَلَّمْتَنِي، وَعَلِّمْنِي مَا يَنْفَعُنِي، وَزِدْنِي عِلْمًا',
    translation_en: 'O Allah, benefit me with what You have taught me, teach me what will benefit me, and increase me in knowledge.',
    category: 'طلب العلم',
    recommendedCount: 1,
    audioSrc: '/audio/allahumma_infani_bima_allamtani.mp3',
  },
  {
    id: 'al-ikhlas',
    arabicText: 'قُلْ هُوَ اللَّهُ أَحَدٌ اللَّهُ الصَّمَدُ لَمْ يَلِدْ وَلَمْ يُولَدْ وَلَمْ يَكُن لَّهُ كُفُوًا أَحَدٌ',
    translation_en: 'Say, "He is Allah, [who is] One. Allah, the Eternal Refuge. He neither begets nor is born, Nor is there to Him any equivalent."',
    category: 'الصباح', // Also evening and before sleep
    recommendedCount: 3,
    virtue_ar: 'قراءة سورة الإخلاص ثلاث مرات تعدل ثلث القرآن.',
    audioSrc: '/audio/al_ikhlas.mp3',
  },
  {
    id: 'al-falaq',
    arabicText: 'قُلْ أَعُوذُ بِرَبِّ الْفَلَقِ مِن شَرِّ مَا خَلَقَ وَمِن شَرِّ غَاسِقٍ إِذَا وَقَبَ وَمِن شَرِّ النَّفَّاثَاتِ فِي الْعُقَدِ وَمِن شَرِّ حَاسِدٍ إِذَا حَسَدَ',
    translation_en: 'Say, "I seek refuge in the Lord of daybreak. From the evil of that which He created. And from the evil of darkness when it settles. And from the evil of the blowers in knots. And from the evil of an envier when he envies."',
    category: 'الصباح', // Also evening and before sleep
    recommendedCount: 3,
    virtue_ar: 'تحفظ من الشرور.',
    audioSrc: '/audio/al_falaq.mp3',
  },
  {
    id: 'an-nas',
    arabicText: 'قُلْ أَعُوذُ بِرَبِّ النَّاسِ مَلِكِ النَّاسِ إِلَهِ النَّاسِ مِن شَرِّ الْوَسْوَاسِ الْخَنَّاسِ الَّذِي يُوَسْوِسُ فِي صُدُورِ النَّاسِ مِنَ الْجِنَّةِ وَالنَّاسِ',
    translation_en: 'Say, "I seek refuge in the Lord of mankind, The Sovereign of mankind. The God of mankind, From the evil of the retreating whisperer - Who whispers [evil] into the breasts of mankind - From among the jinn and mankind."',
    category: 'الصباح', // Also evening and before sleep
    recommendedCount: 3,
    virtue_ar: 'تحفظ من الوسواس.',
    audioSrc: '/audio/an_nas.mp3',
  },
  {
    id: 'allahumma-inni-asbahtu',
    arabicText: 'اللَّهُمَّ إِنِّي أَصْبَحْتُ أُشْهِدُكَ، وَأُشْهِدُ حَمَلَةَ عَرْشِكَ، وَمَلَائِكَتَكَ، وَجَمِيعَ خَلْقِكَ، أَنَّكَ أَنْتَ اللَّهُ لَا إِلَهَ إِلَّا أَنْتَ وَحْدَكَ لَا شَرِيكَ لَكَ، وَأَنَّ مُحَمَّدًا عَبْدُكَ وَرَسُولُكَ',
    translation_en: 'O Allah, I have reached the morning and I call You, the bearers of Your Throne, Your angels, and all of Your creation to witness that You are Allah, none has the right to be worshipped but You Alone, You have no partner, and that Muhammad is Your slave and Messenger.',
    category: 'الصباح',
    recommendedCount: 4,
    virtue_ar: 'من قالها حين يصبح أو يمسي أربع مرات أعتقه الله من النار.',
    audioSrc: '/audio/allahumma_inni_asbahtu.mp3',
  },
  {
    id: 'allahumma-ma-asbaha-bi',
    arabicText: 'اللَّهُمَّ مَا أَصْبَحَ بِي مِنْ نِعْمَةٍ أَوْ بِأَحَدٍ مِنْ خَلْقِكَ فَمِنْكَ وَحْدَكَ لَا شَرِيكَ لَكَ، فَلَكَ الْحَمْدُ وَلَكَ الشُّكْرُ',
    translation_en: 'O Allah, whatever blessing has been received by me or anyone of Your creation is from You alone, You have no partner. All praise is for You and thanks is to You.',
    category: 'الصباح',
    recommendedCount: 1,
    virtue_ar: 'من قالها حين يصبح فقد أدى شكر يومه، ومن قالها حين يمسي فقد أدى شكر ليلته.',
    audioSrc: '/audio/allahumma_ma_asbaha_bi.mp3',
  },
  {
    id: 'allahumma-aafini-fi-badani',
    arabicText: 'اللَّهُمَّ عَافِنِي فِي بَدَنِي، اللَّهُمَّ عَافِنِي فِي سَمْعِي، اللَّهُمَّ عَافِنِي فِي بَصَرِي، لَا إِلَهَ إِلَّا أَنْتَ. اللَّهُمَّ إِنِّي أَعُوذُ بِكَ مِنَ الْكُفْرِ، وَالْفَقْرِ، وَأَعُوذُ بِكَ مِنْ عَذَابِ الْقَبْرِ، لَا إِلَهَ إِلَّا أَنْتَ',
    translation_en: 'O Allah, make me healthy in my body. O Allah, preserve for me my hearing. O Allah, preserve for me my sight. There is none worthy of worship but You. O Allah, I seek refuge in You from disbelief and poverty, and I seek refuge in You from the punishment of the grave. There is none worthy of worship but You.',
    category: 'الصباح',
    recommendedCount: 3,
    audioSrc: '/audio/allahumma_aafini_fi_badani.mp3',
  },
  {
    id: 'allahumma-inni-asalukal-afwa',
    arabicText: 'اللَّهُمَّ إِنِّي أَسْأَلُكَ الْعَفْوَ وَالْعَافِيَةَ فِي الدُّنْيَا وَالْآخِرَةِ، اللَّهُمَّ إِنِّي أَسْأَلُكَ الْعَفْوَ وَالْعَافِيَةَ فِي دِينِي وَدُنْيَايَ وَأَهْلِي وَمَالِي، اللَّهُمَّ اسْتُرْ عَوْرَاتِي وَآمِنْ رَوْعَاتِي، اللَّهُمَّ احْفَظْنِي مِنْ بَيْنِ يَدَيَّ، وَمِنْ خَلْفِي، وَعَنْ يَمِينِي، وَعَنْ شِمَالِي، وَمِنْ فَوْقِي، وَأَعُوذُ بِعَظَمَتِكَ أَنْ أُغْتَالَ مِنْ تَحْتِي',
    translation_en: 'O Allah, I seek Your forgiveness and Your protection in this world and the next. O Allah, I seek Your forgiveness and Your protection in my religion, in my worldly affairs, in my family and in my wealth. O Allah, conceal my secrets and preserve me from anguish. O Allah, guard me from what is in front of me and behind me, from my left, and from my right, and from above me. I seek refuge in Your Greatness from being struck down from beneath me.',
    category: 'الصباح', // Also evening
    recommendedCount: 1,
    audioSrc: '/audio/allahumma_inni_asalukal_afwa.mp3',
  },
  {
    id: 'ya-hayyu-ya-qayyum-birahmatika',
    arabicText: 'يَا حَيُّ يَا قَيُّومُ بِرَحْمَتِكَ أَسْتَغِيثُ، أَصْلِحْ لِي شَأْنِي كُلَّهُ، وَلَا تَكِلْنِي إِلَى نَفْسِي طَرْفَةَ عَيْنٍ',
    translation_en: 'O Ever Living, O Self-Subsisting and Supporter of all, by Your mercy I seek assistance, rectify for me all of my affairs and do not leave me to myself, even for the blink of an eye.',
    category: 'الصباح',
    recommendedCount: 1,
    audioSrc: '/audio/ya_hayyu_ya_qayyum_birahmatika.mp3',
  },
  {
    id: 'subhanallahi-aladheem-wabihamdih',
    arabicText: 'سُبْحَانَ اللَّهِ الْعَظِيمِ وَبِحَمْدِهِ',
    translation_en: 'Glory is to Allah, the Great, and praise is to Him.',
    category: 'عام',
    recommendedCount: 100,
    virtue_ar: 'من قالها مائة مرة حين يصبح وحين يمسي لم يأت أحد يوم القيامة بأفضل مما جاء به إلا أحد قال مثل ما قال أو زاد عليه.',
    audioSrc: '/audio/subhanallahi_aladheem_wabihamdih.mp3',
  },
  {
    id: 'la-hawla-wala-quwwata-illa-billah',
    arabicText: 'لَا حَوْلَ وَلَا قُوَّةَ إِلَّا بِاللَّهِ',
    translation_en: 'There is no might nor power except with Allah.',
    category: 'عام',
    recommendedCount: 1, // Often repeated
    virtue_ar: 'كنز من كنوز الجنة.',
    audioSrc: '/audio/la_hawla_wala_quwwata_illa_billah.mp3',
  },
   {
    id: 'subhanaka-allahumma-wa-bihamdika',
    arabicText: 'سُبْحَانَكَ اللَّهُمَّ وَبِحَمْدِكَ، أَشْهَدُ أَنْ لَا إِلَهَ إِلَّا أَنْتَ، أَسْتَغْفِرُكَ وَأَتُوبُ إِلَيْكَ',
    translation_en: 'Glory is to You, O Allah, and praise is to You. I bear witness that there is none worthy of worship but You. I seek Your forgiveness and repent to You.',
    category: 'ختم المجلس',
    recommendedCount: 1,
    virtue_ar: 'كفارة المجلس.',
    audioSrc: '/audio/subhanaka_allahumma_wa_bihamdika.mp3',
  },
  {
    id: 'allahumma-hdini-wa-saddidni',
    arabicText: 'اللَّهُمَّ اهْدِنِي وَسَدِّدْنِي',
    translation_en: 'O Allah, guide me and make me steadfast.',
    category: 'عام',
    recommendedCount: 1,
    virtue_ar: 'دعاء لطلب الهداية والثبات.',
    audioSrc: '/audio/allahumma_hdini_wa_saddidni.mp3',
  },
  {
    id: 'allahumma-inni-audhubika-minal-ajzi-wal-kasal',
    arabicText: 'اللَّهُمَّ إِنِّي أَعُوذُ بِكَ مِنَ الْعَجْزِ وَالْكَسَلِ، وَالْجُبْنِ وَالْهَرَمِ وَالْبُخْلِ، وَأَعُوذُ بِكَ مِنْ عَذَابِ الْقَبْرِ، وَمِنْ فِتْنَةِ الْمَحْيَا وَالْمَمَاتِ',
    translation_en: 'O Allah, I seek refuge in You from incapacity and laziness, from cowardice, old age, and miserliness. And I seek refuge in You from the torment of the grave, and from the trials of life and death.',
    category: 'عام',
    recommendedCount: 1,
    audioSrc: '/audio/allahumma_inni_audhubika_minal_ajzi_wal_kasal.mp3',
  },
  {
    id: 'allahumma-inni-asaluka-ilman-nafian',
    arabicText: 'اللَّهُمَّ إِنِّي أَسْأَلُكَ عِلْمًا نَافِعًا، وَرِزْقًا طَيِّبًا، وَعَمَلًا مُتَقَبَّلًا',
    translation_en: 'O Allah, I ask You for beneficial knowledge, goodly provision, and acceptable deeds.',
    category: 'الصباح',
    recommendedCount: 1,
    audioSrc: '/audio/allahumma_inni_asaluka_ilman_nafian.mp3',
  },
  {
    id: 'allahumma-inni-abduka',
    arabicText: 'اللَّهُمَّ إِنِّي عَبْدُكَ، ابْنُ عَبْدِكَ، ابْنُ أَمَتِكَ، نَاصِيَتِي بِيَدِكَ، مَاضٍ فِيَّ حُكْمُكَ، عَدْلٌ فِيَّ قَضَاؤُكَ، أَسْأَلُكَ بِكُلِّ اسْمٍ هُوَ لَكَ سَمَّيْتَ بِهِ نَفْسَكَ، أَوْ عَلَّمْتَهُ أَحَدًا مِنْ خَلْقِكَ، أَوْ أَنْزَلْتَهُ فِي كِتَابِكَ، أَوِ اسْتَأْثَرْتَ بِهِ فِي عِلْمِ الْغَيْبِ عِنْدَكَ، أَنْ تَجْعَلَ الْقُرْآنَ رَبِيعَ قَلْبِي، وَنُورَ صَدْرِي، وَجِلَاءَ حُزْنِي، وَذَهَابَ هَمِّي',
    translation_en: 'O Allah, I am Your slave, son of Your slave, son of Your female slave, my forelock is in Your hand, Your command over me is forever executed and Your decree over me is just. I ask You by every name belonging to You which You have named Yourself with, or taught to any of Your creation, or revealed in Your Book, or You have preserved in the knowledge of the unseen with You, that You make the Quran the life of my heart and the light of my breast, and a departure for my sorrow and a release for my anxiety.',
    category: 'عند الكرب',
    recommendedCount: 1,
    virtue_ar: 'دعاء الكرب والهم.',
    audioSrc: '/audio/allahumma_inni_abduka.mp3'
  },
  {
    id: 'allahumma-inni-audhubika-min-jahdil-bala',
    arabicText: 'اللَّهُمَّ إِنِّي أَعُوذُ بِكَ مِنْ جَهْدِ الْبَلَاءِ، وَدَرَكِ الشَّقَاءِ، وَسُوءِ الْقَضَاءِ، وَشَمَاتَةِ الْأَعْدَاءِ',
    translation_en: 'O Allah, I seek refuge in You from the overpowering calamity, from the falling into misery, from the evil of destiny, and from the mockery of enemies.',
    category: 'عام',
    recommendedCount: 1,
    virtue_ar: 'دعاء للاستعاذة من المصائب والشقاء وسوء القضاء وشماتة الأعداء.',
    audioSrc: '/audio/allahumma_inni_audhubika_min_jahdil_bala.mp3'
  },
  {
    id: 'allahumma-aslihli-dini',
    arabicText: 'اللَّهُمَّ أَصْلِحْ لِي دِينِي الَّذِي هُوَ عِصْمَةُ أَمْرِي، وَأَصْلِحْ لِي دُنْيَايَ الَّتِي فِيهَا مَعَاشِي، وَأَصْلِحْ لِي آخِرَتِي الَّتِي فِيهَا مَعَادِي، وَاجْعَلِ الْحَيَاةَ زِيَادَةً لِي فِي كُلِّ خَيْرٍ، وَاجْعَلِ الْمَوْتَ رَاحَةً لِي مِنْ كُلِّ شَرٍّ',
    translation_en: 'O Allah, set right for me my religion which is the safeguard of my affairs, set right for me my worldly affairs which is the means of my livelihood, set right for me my Hereafter which is the place of my return, and make life for me an increase in all good, and make death for me a rest from every evil.',
    category: 'عام',
    recommendedCount: 1,
    virtue_ar: 'دعاء جامع لخير الدنيا والآخرة.',
    audioSrc: '/audio/allahumma_aslihli_dini.mp3'
  },
  {
    id: 'rabbana-atina-fid-dunya',
    arabicText: 'رَبَّنَا آتِنَا فِي الدُّنْيَا حَسَنَةً، وَفِي الْآخِرَةِ حَسَنَةً، وَقِنَا عَذَابَ النَّارِ',
    translation_en: 'Our Lord, give us in this world [that which is] good and in the Hereafter [that which is] good and protect us from the punishment of the Fire.',
    category: 'عام',
    recommendedCount: 1, // Often repeated
    virtue_ar: 'من أكثر دعاء النبي صلى الله عليه وسلم.',
    audioSrc: '/audio/rabbana_atina_fid_dunya.mp3'
  },
  {
    id: 'allahumma-inni-audhubika-min-albaras',
    arabicText: 'اللَّهُمَّ إِنِّي أَعُوذُ بِكَ مِنَ الْبَرَصِ، وَالْجُنُونِ، وَالْجُذَامِ، وَمِنْ سَيِّئِ الْأَسْقَامِ',
    translation_en: 'O Allah, I seek refuge in You from leprosy, madness, elephantiasis, and from evil diseases.',
    category: 'عام',
    recommendedCount: 1,
    virtue_ar: 'دعاء للاستعاذة من الأمراض السيئة.',
    audioSrc: '/audio/allahumma_inni_audhubika_min_albaras.mp3'
  }
];

export const getDailyDhikr = (count: number = 5): Dhikr[] => {
  const morningDhikr = ALL_DHIKR.filter(d => d.category === 'الصباح' || d.category === 'عام' || d.category === 'بعد الصلاة');
  const eveningDhikr = ALL_DHIKR.filter(d => d.category === 'المساء' || d.category === 'عام' || d.category === 'بعد الصلاة');
  
  let relevantDhikr;
  const hour = new Date().getHours();
  if (hour >= 4 && hour < 12) { 
    relevantDhikr = morningDhikr;
  } else if (hour >= 16 && hour < 23) { 
    relevantDhikr = eveningDhikr;
  } else { 
    relevantDhikr = ALL_DHIKR;
  }

  const uniqueDhikr = Array.from(new Set(relevantDhikr.map(d => d.id))).map(id => relevantDhikr.find(d => d.id === id)!);
  const shuffled = uniqueDhikr.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
};

export const getDhikrByCategory = (): Record<string, Dhikr[]> => {
  const categoriesOrder = ['الصباح', 'المساء', 'بعد الصلاة', 'قبل النوم', 'عند الاستيقاظ', 'دخول المسجد', 'الخروج من المسجد', 'بعد الأذان', 'عند الكرب', 'طلب العلم', 'ختم المجلس', 'عام'];
  
  const categorized = ALL_DHIKR.reduce((acc, dhikr) => {
    if (!acc[dhikr.category]) {
      acc[dhikr.category] = [];
    }
    acc[dhikr.category].push(dhikr);
    acc[dhikr.category].sort((a, b) => a.arabicText.localeCompare(b.arabicText, 'ar')); 
    return acc;
  }, {} as Record<string, Dhikr[]>);

  const sortedCategorized: Record<string, Dhikr[]> = {};
  for (const category of categoriesOrder) {
    if (categorized[category]) {
      sortedCategorized[category] = categorized[category];
    }
  }
  for (const category in categorized) {
    if (!sortedCategorized[category]) {
      sortedCategorized[category] = categorized[category];
    }
  }
  return sortedCategorized;
};

export const getDhikrById = (id: string): Dhikr | undefined => {
  return ALL_DHIKR.find(d => d.id === id);
};
