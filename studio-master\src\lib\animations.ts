
import type { Variants } from 'framer-motion';

export const listContainerVariants: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.07, // تقليل التأخير قليلاً
    },
  },
};

export const listItemVariants: Variants = {
  hidden: { opacity: 0, y: 15 }, // تقليل المسافة العمودية قليلاً
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.3, // جعلها أسرع قليلاً
      ease: 'easeOut', // استخدام easeOut لشعور أكثر طبيعية عند الانتهاء
    },
  },
};

export const pageVariants: Variants = {
  initial: { opacity: 0, y: 15 }, // تقليل المسافة العمودية
  in: { opacity: 1, y: 0 },
  out: { opacity: 0, y: -15 }, // تقليل المسافة العمودية
};
