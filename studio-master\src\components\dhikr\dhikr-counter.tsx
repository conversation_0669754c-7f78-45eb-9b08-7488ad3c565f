"use client";

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Repeat, RotateCcw, CheckCircle2, Sparkles } from 'lucide-react';
import { useToast } from "@/hooks/use-toast";

interface DhikrCounterProps {
  dhikrId: string; // Used as part of localStorage key
  recommendedCount?: number;
  onComplete?: () => void; // Callback when recommendedCount is reached
}

export function DhikrCounter({ dhikrId, recommendedCount = 0, onComplete }: DhikrCounterProps) {
  const [count, setCount] = useState(0);
  const { toast } = useToast();

  // Load count from localStorage on mount
  useEffect(() => {
    const storedCount = localStorage.getItem(`dhikr_count_${dhikrId}`);
    if (storedCount) {
      setCount(parseInt(storedCount, 10));
    }
  }, [dhikrId]);

  // Save count to localStorage when it changes
  useEffect(() => {
    localStorage.setItem(`dhikr_count_${dhikrId}`, count.toString());
  }, [count, dhikrId]);

  const isCompleted = recommendedCount > 0 && count >= recommendedCount;

  const increment = () => {
    if (recommendedCount > 0 && count >= recommendedCount) return;
    const newCount = count + 1;
    setCount(newCount);
    if (recommendedCount > 0 && newCount === recommendedCount) {
      toast({
        title: "اكتمل الذكر!",
        description: "ما شاء الله، لقد أكملت العدد الموصى به.",
        action: <Sparkles className="text-accent" />,
      });
      if (onComplete) onComplete();
    }
  };

  const reset = () => {
    setCount(0);
    toast({
      title: "تم إعادة ضبط العداد",
    });
  };

  const progressValue = recommendedCount > 0 ? Math.min((count / recommendedCount) * 100, 100) : 0;

  return (
    <div className="flex flex-col items-center gap-3 p-3 border rounded-lg shadow-inner bg-card w-full sm:w-auto">
      <div className="flex items-center gap-3 w-full justify-center">
        <Button variant="outline" size="icon" onClick={reset} aria-label="إعادة ضبط العداد" className="rounded-full">
          <RotateCcw className="w-5 h-5" />
        </Button>
        <Button 
          variant={isCompleted ? "secondary" : "default"} 
          size="lg" 
          onClick={increment} 
          className="flex-grow sm:flex-grow-0 px-6 py-3 text-lg rounded-md shadow-md hover:shadow-lg focus:ring-2 focus:ring-ring focus:ring-offset-2 transition-all duration-150 ease-in-out transform active:scale-95"
          disabled={isCompleted && recommendedCount > 0}
          aria-label="زيادة العداد"
        >
          {isCompleted && recommendedCount > 0 ? <CheckCircle2 className="w-6 h-6 rtl:ml-2 ltr:mr-2" /> : <Repeat className="w-6 h-6 rtl:ml-2 ltr:mr-2" />}
          <span className="font-mono text-2xl tabular-nums">{count}</span>
        </Button>
         {recommendedCount > 0 && (
            <span className="text-sm text-muted-foreground font-mono self-center">/ {recommendedCount}</span>
          )}
      </div>
      {recommendedCount > 0 && (
        <Progress value={progressValue} className="w-full h-2.5 mt-1 rounded-full" />
      )}
    </div>
  );
}
