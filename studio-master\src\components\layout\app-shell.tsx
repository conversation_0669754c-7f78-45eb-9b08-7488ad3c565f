
"use client";

import Link from "next/link";
import {
  SidebarProvider,
  Sidebar,
  SidebarHeader,
  SidebarContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarInset,
  SidebarTrigger,
  SheetTitle,
} from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";
import { SunMoon, LayoutList, BellRing, Heart, Settings, Quote } from "lucide-react";
import { useEffect, useState } from "react";
import Image from "next/image";
import { motion, AnimatePresence } from 'framer-motion';
import { usePathname } from 'next/navigation';
import { pageVariants } from '@/lib/animations'; 

// A simple ThemeToggle
const ThemeToggle = () => {
  const [mounted, setMounted] = useState(false);
  const [currentTheme, setCurrentTheme] = useState("light");

  useEffect(() => setMounted(true), []);

  useEffect(() => {
    if (mounted) {
        const storedTheme = localStorage.getItem('theme');
        if (storedTheme === 'dark' || (!storedTheme && window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
            document.documentElement.classList.add('dark');
            setCurrentTheme('dark');
        } else {
            document.documentElement.classList.remove('dark');
            setCurrentTheme('light');
        }
    }
  }, [mounted]);


  if (!mounted) {
    return <Button variant="ghost" size="icon" disabled><SunMoon className="h-5 w-5" /></Button>;
  }

  const toggleTheme = () => {
    const newTheme = currentTheme === "dark" ? "light" : "dark";
    setCurrentTheme(newTheme);
    if (newTheme === 'dark') {
        document.documentElement.classList.add('dark');
        localStorage.setItem('theme', 'dark');
    } else {
        document.documentElement.classList.remove('dark');
        localStorage.setItem('theme', 'light');
    }
  };

  return (
    <Button variant="ghost" size="icon" onClick={toggleTheme} aria-label="Toggle theme">
      <SunMoon className="h-5 w-5" />
    </Button>
  );
};


export function AppShell({ children }: { children: React.ReactNode }) {
  const [defaultOpen, setDefaultOpen] = useState(true);
  const pathname = usePathname();

  useEffect(() => {
    if (typeof window !== "undefined" && window.innerWidth < 768) {
      setDefaultOpen(false);
    }
  }, []);

  const pageTransition = { 
    type: "tween", // يمكن استخدام "spring" لشعور أكثر طبيعية إذا أردت
    ease: "anticipate", // نوع حركة يعطي شعور بالاستعداد ثم الحركة
    duration: 0.5, // تقليل المدة لجعلها أسرع
  };

  return (
    <SidebarProvider defaultOpen={defaultOpen}>
      <Sidebar collapsible="icon" className="border-e shadow-sm">
        <SidebarHeader className="p-4 flex flex-col items-center">
          <Link href="/" className="flex items-center gap-2 text-xl font-semibold text-sidebar-primary hover:text-sidebar-primary/90 transition-colors">
             <Image 
               src="https://picsum.photos/40/40" 
               alt="رفيق الذكر Logo" 
               width={32} 
               height={32} 
               className="rounded-full"
               data-ai-hint="logo placeholder" 
             />
            <span className="group-data-[collapsible=icon]:hidden font-arabic">رفيق الذكر</span>
          </Link>
        </SidebarHeader>
        <SidebarContent className="p-2">
          <SidebarMenu>
            <SidebarMenuItem>
              <SidebarMenuButton asChild isActive={pathname === "/"} tooltip="الذكر اليومي">
                <Link href="/">
                  <Quote className="h-5 w-5" /> <span className="group-data-[collapsible=icon]:hidden font-arabic">الذكر اليومي</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton asChild isActive={pathname === "/index-dhikr"} tooltip="فهرس الأذكار">
                <Link href="/index-dhikr">
                  <LayoutList className="h-5 w-5" /> <span className="group-data-[collapsible=icon]:hidden font-arabic">فهرس الأذكار</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
             <SidebarMenuItem>
              <SidebarMenuButton asChild isActive={pathname === "/favorites"} tooltip="الأذكار المفضلة">
                <Link href="/favorites">
                  <Heart className="h-5 w-5" /> <span className="group-data-[collapsible=icon]:hidden font-arabic">الأذكار المفضلة</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
            <SidebarMenuItem>
              <SidebarMenuButton asChild isActive={pathname === "/reminders"} tooltip="التذكيرات">
                <Link href="/reminders">
                  <BellRing className="h-5 w-5" /> <span className="group-data-[collapsible=icon]:hidden font-arabic">التذكيرات</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
             <SidebarMenuItem>
              <SidebarMenuButton asChild isActive={pathname === "/settings"} tooltip="الإعدادات">
                <Link href="/settings">
                  <Settings className="h-5 w-5" /> <span className="group-data-[collapsible=icon]:hidden font-arabic">الإعدادات</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarContent>
      </Sidebar>
      <SidebarInset>
        <header className="sticky top-0 z-40 flex items-center h-16 px-4 border-b bg-background/80 backdrop-blur-sm md:px-6">
          <div className="md:hidden">
            <SidebarTrigger className="text-foreground" />
          </div>
          <div className="flex-1" /> {/* Spacer */}
          <ThemeToggle />
        </header>
        <main className="flex-1 p-4 md:p-8 bg-background overflow-hidden">
          <AnimatePresence mode="wait" initial={false}>
            <motion.div
              key={pathname}
              initial="initial"
              animate="in"
              exit="out"
              variants={pageVariants}
              transition={pageTransition}
              className="h-full w-full"
            >
              <div className="max-w-5xl mx-auto h-full">
                {children}
              </div>
            </motion.div>
          </AnimatePresence>
        </main>
        <footer className="py-4 text-center border-t text-xs text-muted-foreground">
          © {new Date().getFullYear()} رفيق الذكر. كل الحقوق محفوظة.
        </footer>
      </SidebarInset>
    </SidebarProvider>
  );
}
