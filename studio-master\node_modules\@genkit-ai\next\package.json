{"name": "@genkit-ai/next", "description": "Next.js plugin for Genkit", "keywords": ["genkit", "genkit-plugin", "google cloud", "google ai", "ai", "genai", "generative-ai", "next", "nextjs", "next.js", "react"], "version": "1.8.0", "type": "commonjs", "main": "lib/index.js", "repository": {"type": "git", "url": "https://github.com/firebase/genkit.git", "directory": "js/plugins/next"}, "author": "genkit", "license": "Apache-2.0", "peerDependencies": {"next": "^15.0.0", "zod": "^3.24.1", "genkit": "1.8.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/jest": "^29.5.12", "@types/node": "^20.11.16", "@types/react": "^19", "@types/react-dom": "^19", "jest": "^29.7.0", "next": "^15.2.4", "npm-run-all": "^4.1.5", "rimraf": "^6.0.1", "ts-jest": "^29.1.2", "tsup": "^8.0.2", "tsx": "^4.7.0", "typescript": "^4.9.0", "zod": "^3.24.1", "genkit": "1.8.0"}, "types": "./lib/index.d.ts", "exports": {".": {"require": "./lib/index.js", "import": "./lib/index.mjs", "types": "./lib/index.d.ts", "default": "./lib/index.js"}, "./client": {"require": "./lib/client.js", "import": "./lib/client.mjs", "types": "./lib/client.d.ts", "default": "./lib/client.js"}}, "typesVersions": {"*": {"client": ["lib/client"]}}, "scripts": {"check": "tsc", "compile": "tsup-node", "build:clean": "rimraf ./lib", "build": "npm-run-all build:clean check compile", "build:watch": "tsup-node --watch", "test": "jest --verbose"}}