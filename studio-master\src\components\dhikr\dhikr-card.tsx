
"use client";

import type { Dhikr } from '@/types/dhikr';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/ui/card';
import { DhikrCounter } from './dhikr-counter';
import { Badge } from '@/components/ui/badge';
import { BookOpenText, Target, Heart, Sparkles, Loader2 } from 'lucide-react';
import { Button } from '../ui/button';
import { useFavoriteDhikr } from '@/hooks/use-favorite-dhikr';
import { cn } from '@/lib/utils';
import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { listItemVariants } from '@/lib/animations';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import { getDhikrInsight, type DhikrInsightOutput } from '@/ai/flows/dhikr-insight-flow';
import { Skeleton } from '@/components/ui/skeleton';

export function DhikrCard({ dhikr }: { dhikr: Dhikr }) {
  const { isFavorite, toggleFavorite } = useFavoriteDhikr();
  const [isClientMounted, setIsClientMounted] = useState(false);

  const [isInsightDialogOpen, setIsInsightDialogOpen] = useState(false);
  const [insightText, setInsightText] = useState<string | null>(null);
  const [isLoadingInsight, setIsLoadingInsight] = useState(false);

  useEffect(() => {
    setIsClientMounted(true);
  }, []);

  const isCurrentlyFavorite = isClientMounted ? isFavorite(dhikr.id) : false;

  const handleFetchInsight = async () => {
    setIsInsightDialogOpen(true);
    setIsLoadingInsight(true);
    setInsightText(null); 
    try {
      const result: DhikrInsightOutput = await getDhikrInsight({
        arabicText: dhikr.arabicText,
        currentVirtue: dhikr.virtue_ar,
      });
      setInsightText(result.insight);
    } catch (error) {
      console.error("Error fetching insight:", error);
      setInsightText("عذرًا، حدث خطأ أثناء محاولة جلب الرؤى الإضافية. يرجى المحاولة مرة أخرى.");
    } finally {
      setIsLoadingInsight(false);
    }
  };

  return (
    <>
      <motion.div
        variants={listItemVariants}
        whileHover={{ scale: 1.02 }}
        className="w-full"
      >
        <Card className="w-full shadow-lg hover:shadow-xl transition-shadow duration-300 bg-card rounded-xl overflow-hidden">
          <CardHeader className="p-6">
            <div className="flex justify-between items-start gap-4">
              <div className="flex-1 space-y-2">
                <CardTitle className="text-3xl font-arabic leading-relaxed text-right text-foreground">{dhikr.arabicText}</CardTitle>
              </div>
              <div className="flex flex-col items-center space-y-1">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={handleFetchInsight}
                  aria-label="عرض رؤى إضافية"
                  className="text-muted-foreground hover:text-primary"
                >
                  <Sparkles className="w-5 h-5" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => toggleFavorite(dhikr.id)}
                  aria-label={isClientMounted ? (isCurrentlyFavorite ? "إزالة من المفضلة" : "إضافة للمفضلة") : "إضافة للمفضلة"}
                  className={cn(
                    "text-muted-foreground hover:text-destructive",
                    isClientMounted && isCurrentlyFavorite && "text-destructive hover:text-destructive/80"
                  )}
                >
                  <Heart className={cn("w-5 h-5", isClientMounted && isCurrentlyFavorite && "fill-destructive")} />
                </Button>
              </div>
            </div>
            {dhikr.translation_en && (
              <CardDescription className="text-sm text-muted-foreground text-left pt-3 border-t mt-3">{dhikr.translation_en}</CardDescription>
            )}
          </CardHeader>
          {dhikr.virtue_ar && (
            <CardContent className="p-6 pt-0">
              <div className="mt-2 p-4 bg-secondary/30 rounded-lg border border-secondary">
                <h4 className="text-md font-semibold mb-2 text-right flex items-center justify-end gap-2 text-primary">
                  <BookOpenText className="w-4 h-4" />
                  فضل الذكر
                </h4>
                <p className="text-sm text-muted-foreground text-right leading-relaxed font-arabic">{dhikr.virtue_ar}</p>
              </div>
            </CardContent>
          )}
          <CardFooter className="flex flex-col sm:flex-row justify-between items-center gap-4 p-6 bg-muted/20 border-t">
            <div className="flex flex-wrap items-center gap-2">
              <Badge variant="outline" className="text-sm py-1 px-3">{dhikr.category}</Badge>
              {dhikr.recommendedCount && dhikr.recommendedCount > 0 && (
                <Badge variant="secondary" className="flex items-center gap-1.5 text-sm py-1 px-3">
                  <Target className="w-4 h-4" />
                  {dhikr.recommendedCount} مرة
                </Badge>
              )}
            </div>
            <DhikrCounter dhikrId={dhikr.id} recommendedCount={dhikr.recommendedCount} />
          </CardFooter>
        </Card>
      </motion.div>

      <Dialog open={isInsightDialogOpen} onOpenChange={setIsInsightDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="text-right font-arabic text-2xl text-primary">تأملات وفوائد إضافية حول:</DialogTitle>
            <DialogDescription className="text-right font-arabic text-lg py-2 border-b">
              {dhikr.arabicText.substring(0, 50)}{dhikr.arabicText.length > 50 ? '...' : ''}
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 min-h-[100px]">
            {isLoadingInsight && (
              <div className="space-y-2">
                <Skeleton className="h-4 w-[250px]" />
                <Skeleton className="h-4 w-[200px]" />
                <Skeleton className="h-4 w-[220px]" />
              </div>
            )}
            {insightText && !isLoadingInsight && (
              <p className="text-md text-foreground leading-relaxed font-arabic text-right">{insightText}</p>
            )}
          </div>
          <DialogFooter className="sm:justify-start">
            <DialogClose asChild>
              <Button type="button" variant="secondary" className="font-arabic">
                إغلاق
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
