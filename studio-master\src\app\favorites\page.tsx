
"use client";

import { DhikrCard } from "@/components/dhikr/dhikr-card";
import { useFavoriteDhikr } from "@/hooks/use-favorite-dhikr";
import { ALL_DHIKR } from "@/lib/dhikrData";
import type { Dhikr } from "@/types/dhikr";
import { Separator } from "@/components/ui/separator";
import Image from "next/image";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { HeartCrack, Heart } from "lucide-react";
import { motion } from "framer-motion";
import { listContainerVariants } from "@/lib/animations";

export default function FavoritesPage() {
  const { favoriteDhikrIds } = useFavoriteDhikr();

  const favoriteDhikrList: Dhikr[] = ALL_DHIKR.filter(dhikr => 
    favoriteDhikrIds.includes(dhikr.id)
  ).sort((a,b) => favoriteDhikrIds.indexOf(a.id) - favoriteDhikrIds.indexOf(b.id));


  return (
    <div className="space-y-8">
      <header className="text-center space-y-4 py-8 bg-gradient-to-b from-background to-secondary/30 rounded-xl shadow-sm">
        <Image 
          src="https://picsum.photos/1200/250" 
          alt="Favorite Dhikr Banner" 
          width={1200} 
          height={250} 
          className="w-full h-40 object-cover rounded-lg shadow-md"
          data-ai-hint="favorite banner"
        />
        <h1 className="text-4xl md:text-5xl font-bold tracking-tight text-primary font-arabic">الأذكار المفضلة</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          هنا تجد قائمة الأذكار التي قمت بتفضيلها للوصول السريع إليها.
        </p>
      </header>
      
      <Separator />

      {favoriteDhikrList.length > 0 ? (
        <motion.div 
          className="grid gap-6 md:gap-8"
          variants={listContainerVariants}
          initial="hidden"
          animate="visible"
        >
          {favoriteDhikrList.map((dhikr) => (
            <DhikrCard key={dhikr.id} dhikr={dhikr} />
          ))}
        </motion.div>
      ) : (
        <div className="text-center py-12 bg-card p-8 rounded-xl shadow-md">
          <HeartCrack className="w-24 h-24 text-muted-foreground mx-auto mb-6" />
          <h2 className="text-2xl font-semibold text-foreground mb-3 font-arabic">قائمة المفضلة فارغة</h2>
          <p className="text-lg text-muted-foreground mb-6">
            لم تقم بإضافة أي أذكار إلى قائمة المفضلة بعد.
          </p>
          <p className="text-muted-foreground mb-6">
            يمكنك تصفح الأذكار وإضافة ما يعجبك بالضغط على أيقونة القلب <Heart className="inline w-4 h-4 text-destructive" />.
          </p>
          <Button asChild size="lg">
            <Link href="/index-dhikr">تصفح جميع الأذكار</Link>
          </Button>
        </div>
      )}
    </div>
  );
}
