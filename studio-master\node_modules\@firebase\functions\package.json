{"name": "@firebase/functions", "version": "0.12.4", "description": "", "author": "Firebase <<EMAIL>> (https://firebase.google.com/)", "main": "dist/index.cjs.js", "browser": "dist/esm/index.esm2017.js", "module": "dist/esm/index.esm2017.js", "exports": {".": {"types": "./dist/functions-public.d.ts", "node": {"import": "./dist/esm/index.esm2017.js", "require": "./dist/index.cjs.js"}, "browser": {"require": "./dist/index.cjs.js", "import": "./dist/esm/index.esm2017.js"}, "default": "./dist/esm/index.esm2017.js"}, "./package.json": "./package.json"}, "files": ["dist"], "scripts": {"lint": "eslint -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "lint:fix": "eslint --fix -c .eslintrc.js '**/*.ts' --ignore-path '../../.gitignore'", "build": "rollup -c && yarn api-report", "build:deps": "lerna run --scope @firebase/functions --include-dependencies build", "build:release": "rollup -c rollup.config.release.js && yarn api-report", "dev": "rollup -c -w", "test": "run-p --npm-path npm lint test:all", "test:ci": "node ../../scripts/run_tests_in_ci.js -s test:all", "test:all": "run-p --npm-path npm test:browser test:node", "test:browser": "karma start", "test:browser:debug": "karma start --browsers=Chrome --auto-watch", "test:node": "TS_NODE_COMPILER_OPTIONS='{\"module\":\"commonjs\"}' nyc --reporter lcovonly -- mocha 'src/{,!(browser)/**/}*.test.ts' --file src/index.ts --config ../../config/mocharc.node.js", "test:emulator": "env FIREBASE_FUNCTIONS_EMULATOR_ORIGIN=http://127.0.0.1:5005 run-p --npm-path npm test:node", "trusted-type-check": "tsec -p tsconfig.json --noEmit", "api-report": "api-extractor run --local --verbose", "doc": "api-documenter markdown --input temp --output docs", "build:doc": "yarn build && yarn doc", "typings:public": "node ../../scripts/build/use_typings.js ./dist/functions-public.d.ts"}, "license": "Apache-2.0", "peerDependencies": {"@firebase/app": "0.x"}, "devDependencies": {"@firebase/app": "0.12.0", "rollup": "2.79.2", "@rollup/plugin-json": "6.1.0", "rollup-plugin-typescript2": "0.36.0", "typescript": "5.5.4"}, "repository": {"directory": "packages/functions", "type": "git", "url": "git+https://github.com/firebase/firebase-js-sdk.git"}, "bugs": {"url": "https://github.com/firebase/firebase-js-sdk/issues"}, "typings": "./dist/functions-public.d.ts", "dependencies": {"@firebase/component": "0.6.14", "@firebase/messaging-interop-types": "0.2.3", "@firebase/auth-interop-types": "0.2.4", "@firebase/app-check-interop-types": "0.3.3", "@firebase/util": "1.11.1", "tslib": "^2.1.0"}, "nyc": {"extension": [".ts"], "reportDir": "./coverage/node"}, "engines": {"node": ">=18.0.0"}}