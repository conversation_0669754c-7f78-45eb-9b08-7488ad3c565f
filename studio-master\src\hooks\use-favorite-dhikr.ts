// src/hooks/use-favorite-dhikr.ts
"use client";

import { useCallback } from 'react';
import { useLocalStorage } from './use-local-storage';
import { useToast } from './use-toast';
import type { Dhikr } from '@/types/dhikr'; // Import Dhikr type if needed for toast messages
import { getDhikrById } from '@/lib/dhikrData'; // To get Dhikr details for toast

const FAVORITES_STORAGE_KEY = 'dhikr_favorites';

export function useFavoriteDhikr() {
  const [favoriteDhikrIds, setFavoriteDhikrIds] = useLocalStorage<string[]>(FAVORITES_STORAGE_KEY, []);
  const { toast } = useToast();

  const isFavorite = useCallback(
    (id: string): boolean => {
      return favoriteDhikrIds.includes(id);
    },
    [favoriteDhikrIds]
  );

  const toggleFavorite = useCallback(
    (id: string) => {
      const dhikrDetails = getDhikrById(id);
      const dhikrName = dhikrDetails ? dhikrDetails.arabicText.substring(0, 30) + "..." : "الذكر";

      setFavoriteDhikrIds(prevIds => {
        if (prevIds.includes(id)) {
          toast({
            title: "تم الإزالة من المفضلة",
            description: `${dhikrName} لم يعد في قائمة مفضلاتك.`,
          });
          return prevIds.filter(favId => favId !== id);
        } else {
          toast({
            title: "أضيف إلى المفضلة!",
            description: `تمت إضافة ${dhikrName} إلى مفضلاتك.`,
          });
          return [...prevIds, id];
        }
      });
    },
    [setFavoriteDhikrIds, toast]
  );

  return { favoriteDhikrIds, isFavorite, toggleFavorite };
}
