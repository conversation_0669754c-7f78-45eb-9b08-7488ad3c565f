
"use client";

import { useEffect } from 'react';
import { useSettings } from '@/contexts/settings-context';

export function ApplyGlobalSettings() {
  const { settings, availableFonts } = useSettings();

  useEffect(() => {
    // Remove all possible font setting classes
    availableFonts.forEach(font => {
      document.body.classList.remove(`font-setting-${font.value}`);
    });
    
    // Add the current font setting class
    if (settings.arabicFont) {
      document.body.classList.add(`font-setting-${settings.arabicFont}`);
    }
  }, [settings.arabicFont, availableFonts]);

  return null; // This component does not render anything itself
}
