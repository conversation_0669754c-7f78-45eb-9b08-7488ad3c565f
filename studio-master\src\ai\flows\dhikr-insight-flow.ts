
'use server';
/**
 * @fileOverview A Genkit flow to generate additional insights for a given Dhikr.
 *
 * - getDhikrInsight - A function that handles the Dhikr insight generation.
 * - DhikrInsightInput - The input type for the getDhikrInsight function.
 * - DhikrInsightOutput - The return type for the getDhikrInsight function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const DhikrInsightInputSchema = z.object({
  arabicText: z.string().describe('The Arabic text of the Dhikr.'),
  currentVirtue: z.string().optional().describe('The known virtue of the Dhikr, if available.'),
});
export type DhikrInsightInput = z.infer<typeof DhikrInsightInputSchema>;

const DhikrInsightOutputSchema = z.object({
  insight: z.string().describe('A brief, insightful, and encouraging elaboration about the Dhikr in Arabic.'),
});
export type DhikrInsightOutput = z.infer<typeof DhikrInsightOutputSchema>;

export async function getDhikrInsight(input: DhikrInsightInput): Promise<DhikrInsightOutput> {
  return dhikrInsightFlow(input);
}

const prompt = ai.definePrompt({
  name: 'dhikrInsightPrompt',
  input: {schema: DhikrInsightInputSchema},
  output: {schema: DhikrInsightOutputSchema},
  prompt: `أنت عالم إسلامي واسع المعرفة ولطيف. يرغب المستخدم في فهم المزيد عن ذكر معين.
بالنظر إلى النص العربي للذكر وفضيلته المعروفة (إن وجد)، يرجى تقديم شرح موجز (2-3 جمل) ثاقب ومشجع.
ركز على جعل الشرح سهل الفهم وملهمًا. تجنب المصطلحات اللاهوتية المعقدة. يجب أن يكون الناتج باللغة العربية الفصحى المبسطة.

الذكر (النص العربي): {{{arabicText}}}
الفضيلة المعروفة: {{#if currentVirtue}}{{{currentVirtue}}}{{else}}غير محددة.{{/if}}

قدم شرحك الثاقب والملهم أدناه:`,
});

const dhikrInsightFlow = ai.defineFlow(
  {
    name: 'dhikrInsightFlow',
    inputSchema: DhikrInsightInputSchema,
    outputSchema: DhikrInsightOutputSchema,
  },
  async (input) => {
    const {output} = await prompt(input);
    if (!output) {
      return { insight: "لم يتمكن الذكاء الاصطناعي من إنشاء رؤية إضافية لهذا الذكر حاليًا. يرجى المحاولة مرة أخرى." };
    }
    return output;
  }
);
