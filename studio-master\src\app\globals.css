
@import url('https://fonts.googleapis.com/css2?family=Amiri:ital,wght@0,400;0,700;1,400;1,700&family=Noto+Naskh+Arabic:wght@400..700&family=Scheherazade+New:wght@400;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
}

.font-arabic {
  /* Base Arabic font style, will be overridden by specific settings */
  font-family: 'Times New Roman', Times, serif;
}

/* Arabic font settings based on body class */
body.font-setting-default .font-arabic {
  font-family: 'Times New Roman', Times, serif;
}
body.font-setting-noto-naskh .font-arabic {
  font-family: 'Noto Naskh Arabic', serif;
}
body.font-setting-amiri .font-arabic {
  font-family: 'Amiri', serif;
}
body.font-setting-scheherazade .font-arabic {
  font-family: 'Scheherazade New', serif;
}


@layer base {
  :root {
    --background: 207 89% 94%; /* Calm Blue - #E3F2FD */
    --foreground: 210 20% 25%; /* Darker <PERSON> for text on Calm Blue */
    
    --card: 0 0% 98%; /* Neutral Gray - #FAFAFA */
    --card-foreground: 210 20% 25%; /* Text on Neutral Gray */
    
    --popover: 0 0% 98%; /* Neutral Gray - #FAFAFA for popovers */
    --popover-foreground: 210 20% 25%; /* Text on Neutral Gray */
    
    --primary: 125 40% 75%; /* Soft Green - #A5D6A7 */
    --primary-foreground: 125 30% 20%; /* Darker Green for text on Soft Green */
    
    --secondary: 207 60% 90%; /* Lighter shade of calm blue */
    --secondary-foreground: 210 20% 25%;
    
    --muted: 207 40% 88%; /* Muted calm blue */
    --muted-foreground: 210 15% 40%;
    
    --accent: 125 40% 75%; /* Soft Green - #A5D6A7 */
    --accent-foreground: 125 30% 20%; /* Darker Green for text on Soft Green */
    
    --destructive: 0 72% 51%; /* Default red, slightly adjusted */
    --destructive-foreground: 0 0% 98%;
    
    --border: 207 30% 82%; /* Border derived from calm blue */
    --input: 207 30% 88%; /* Input derived from calm blue */
    --ring: 125 45% 65%; /* Soft Green for focus rings, slightly darker variant of primary */

    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    /* Sidebar specific colors - Modernized */
    --sidebar-background: 210 25% 96%; /* Slightly cooler, very light gray */
    --sidebar-foreground: 210 20% 20%; /* Darker text for better contrast */
    --sidebar-primary: 125 45% 50%;    /* Primary accent for active items - slightly punchier green */
    --sidebar-primary-foreground: 0 0% 100%; /* White text on primary */
    --sidebar-accent: 125 40% 90%;     /* Lighter green for hover backgrounds */
    --sidebar-accent-foreground: 125 30% 25%; /* Darker green text for hover */
    --sidebar-border: 210 20% 88%;     /* Softer border */
    --sidebar-ring: 125 45% 50%;
  }

  .dark {
    --background: 210 20% 12%; /* Dark desaturated blue */
    --foreground: 210 30% 90%; /* Light gray/blue for text */
    
    --card: 210 20% 10%; /* Darker card background for dark mode */
    --card-foreground: 210 30% 90%;
    
    --popover: 210 20% 10%; /* Darker popover background */
    --popover-foreground: 210 30% 90%;
    
    --primary: 125 45% 70%; /* Adjusted Soft Green for dark mode visibility */
    --primary-foreground: 125 25% 15%; /* Dark Green for text on dark mode primary */
    
    --secondary: 210 20% 20%; /* Darker Neutral Gray/Blue */
    --secondary-foreground: 210 30% 90%;
    
    --muted: 210 20% 22%;
    --muted-foreground: 210 30% 65%;
    
    --accent: 125 45% 70%; /* Consistent with primary for dark mode */
    --accent-foreground: 125 25% 15%; /* Consistent with primary-foreground for dark mode */
    
    --destructive: 0 63% 40%;
    --destructive-foreground: 0 0% 98%;
    
    --border: 210 20% 25%;
    --input: 210 20% 25%;
    --ring: 125 45% 60%; /* Adjusted ring for dark mode */

    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Sidebar specific colors for dark mode - Modernized */
    --sidebar-background: 210 20% 8%;  /* Even darker, almost black-blue for a modern feel */
    --sidebar-foreground: 210 30% 90%; /* Light text for contrast */
    --sidebar-primary: 125 50% 65%;    /* Brighter green accent for dark mode */
    --sidebar-primary-foreground: 0 0% 100%; /* White text on primary */
    --sidebar-accent: 210 20% 14%;     /* Subtle dark hover background */
    --sidebar-accent-foreground: 125 50% 75%; /* Lighter, brighter green for hover text */
    --sidebar-border: 210 15% 20%;     /* Subtle border */
    --sidebar-ring: 125 50% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
