
"use client";

import { <PERSON>minderForm } from "@/components/reminders/reminder-form";
import { <PERSON>minderList } from "@/components/reminders/reminder-list";
import { useLocalStorage } from "@/hooks/use-local-storage";
import type { <PERSON>minder } from "@/types/reminder";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import Image from "next/image";

const REMINDERS_STORAGE_KEY = 'dhikr_reminders';

export default function RemindersPage() {
  const [reminders, setReminders] = useLocalStorage<Reminder[]>(REMINDERS_STORAGE_KEY, []);
  const { toast } = useToast();

  const addReminder = (newReminder: Reminder) => {
    setReminders(prev => [...prev, newReminder].sort((a,b) => a.time.localeCompare(b.time)));
  };

  const toggleReminder = (id: string) => {
    setReminders(prev => 
      prev.map(r => 
        r.id === id ? { ...r, isActive: !r.isActive } : r
      )
    );
    const reminder = reminders.find(r => r.id === id);
    if (reminder) {
        toast({
            title: reminder.isActive ? "تم تعطيل التذكير" : "تم تفعيل التذكير",
        });
    }
  };

  const deleteReminder = (id: string) => {
    setReminders(prev => prev.filter(r => r.id !== id));
    toast({
        title: "تم حذف التذكير",
        variant: "destructive"
    });
  };

  return (
    <div className="space-y-10">
      <header className="text-center space-y-4 py-8 bg-gradient-to-b from-background to-secondary/30 rounded-xl shadow-sm">
        <Image 
          src="https://picsum.photos/1200/200" 
          alt="Mosque silhouette banner" 
          width={1200} 
          height={200} 
          className="w-full h-36 object-cover rounded-lg shadow-md"
          data-ai-hint="mosque silhouette"
        />
        <h1 className="text-4xl md:text-5xl font-bold tracking-tight text-primary font-arabic">إدارة التذكيرات</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          قم بإعداد تذكيرات للأذكار في الأوقات التي تناسبك. (ملاحظة: هذه الواجهة لا ترسل إشعارات فعلية بعد).
        </p>
      </header>

      <ReminderForm onAddReminder={addReminder} />
      
      <Separator />
      
      <div>
        <h2 className="text-3xl font-semibold mb-6 text-center font-arabic text-primary">قائمة التذكيرات</h2>
        <ReminderList 
          reminders={reminders} 
          onToggleReminder={toggleReminder} 
          onDeleteReminder={deleteReminder} 
        />
      </div>
    </div>
  );
}
