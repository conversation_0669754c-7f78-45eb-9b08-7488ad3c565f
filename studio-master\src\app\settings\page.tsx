
"use client";

import { Separator } from "@/components/ui/separator";
import Image from "next/image";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useSettings, type ArabicFont } from "@/contexts/settings-context";
import { Palette } from "lucide-react"; // Using Palette for appearance settings

export default function SettingsPage() {
  const { settings, updateSetting, availableFonts } = useSettings();

  const handleFontChange = (value: string) => {
    updateSetting('arabicFont', value as ArabicFont);
  };

  return (
    <div className="space-y-8">
      <header className="text-center space-y-4 py-8 bg-gradient-to-b from-background to-secondary/30 rounded-xl shadow-sm">
        <Image 
          src="https://picsum.photos/1200/200" 
          alt="Settings Banner" 
          width={1200} 
          height={200} 
          className="w-full h-36 object-cover rounded-lg shadow-md"
          data-ai-hint="gears abstract"
        />
        <h1 className="text-4xl md:text-5xl font-bold tracking-tight text-primary font-arabic">الإعدادات</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          قم بتخصيص تفضيلات التطبيق لتناسب تجربتك.
        </p>
      </header>
      
      <Separator />

      <Card className="w-full shadow-lg">
        <CardHeader>
          <CardTitle className="text-2xl font-arabic flex items-center gap-2">
            <Palette className="w-7 h-7 text-primary" />
            إعدادات المظهر
          </CardTitle>
          <CardDescription>
            اختر الخط العربي المفضل لديك لعرض الأذكار والنصوص العربية الأخرى في التطبيق.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6 pt-6">
          <div>
            <Label htmlFor="arabic-font-select" className="text-lg font-semibold mb-2 block font-arabic">
              الخط العربي:
            </Label>
            <Select value={settings.arabicFont} onValueChange={handleFontChange}>
              <SelectTrigger id="arabic-font-select" className="w-full md:w-1/2 text-lg py-3">
                <SelectValue placeholder="اختر خطًا" />
              </SelectTrigger>
              <SelectContent>
                {availableFonts.map(font => (
                  <SelectItem key={font.value} value={font.value} className="text-md font-arabic text-right py-2">
                    {font.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="mt-6 p-4 border rounded-lg bg-card">
            <h3 className="text-xl font-semibold mb-3 font-arabic text-primary">معاينة الخط:</h3>
            <p className={`text-2xl leading-relaxed ${settings.arabicFont === 'default' ? 'font-arabic' : `font-arabic`}`}>
              بِسْمِ اللَّهِ الرَّحْمَنِ الرَّحِيمِ
            </p>
            <p className={`mt-2 text-lg leading-relaxed ${settings.arabicFont === 'default' ? 'font-arabic' : `font-arabic`}`}>
              الْحَمْدُ لِلَّهِ رَبِّ الْعَالَمِينَ
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Future settings sections can be added here */}
      {/* 
      <Card className="w-full shadow-lg mt-8">
        <CardHeader>
          <CardTitle className="text-2xl font-arabic flex items-center gap-2">
             Notification Settings Icon 
            إعدادات الإشعارات (قيد التطوير)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">سيتم إضافة خيارات تخصيص الإشعارات قريبًا.</p>
        </CardContent>
      </Card>
      */}
    </div>
  );
}
