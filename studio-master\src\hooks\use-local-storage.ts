
"use client";

import { useState, useEffect, useCallback, useRef } from 'react';

// Helper to safely parse JSON
function safeJsonParse<T>(jsonString: string | null, defaultValue: T): T {
  if (!jsonString) return defaultValue;
  try {
    return JSON.parse(jsonString) as T;
  } catch (e) {
    console.warn('Failed to parse JSON from localStorage:', e);
    return defaultValue;
  }
}

export function useLocalStorage<T>(
  key: string,
  initialValueProp: T | (() => T)
): [T, React.Dispatch<React.SetStateAction<T>>] {
  
  // Store the initialValueProp in a ref. This ref's .current property
  // will be updated if initialValueProp changes, but the ref object itself is stable.
  const initialValueRef = useRef(initialValueProp);
  useEffect(() => {
    initialValueRef.current = initialValueProp;
  }, [initialValueProp]);

  const [storedValue, setStoredValue] = useState<T>(() => {
    // This initializer function for useState runs only once.
    // It uses initialValueRef.current which holds the initialValueProp
    // at the time of the first render.
    const actualInitialValue =
      typeof initialValueRef.current === 'function'
        ? (initialValueRef.current as () => T)()
        : initialValueRef.current;

    if (typeof window === 'undefined') {
      return actualInitialValue;
    }
    try {
      const item = window.localStorage.getItem(key);
      return item ? safeJsonParse<T>(item, actualInitialValue) : actualInitialValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}" on init:`, error);
      return actualInitialValue;
    }
  });

  const setValue: React.Dispatch<React.SetStateAction<T>> = useCallback(
    value => {
      if (typeof window === 'undefined') {
        console.warn(`Tried setting localStorage key “${key}” in a non-client environment.`);
        // Update state optimistically even if localStorage fails or isn't available
        // This ensures server-rendered components can still "set" the value for their own state.
        setStoredValue(prev => value instanceof Function ? value(prev) : value);
        return;
      }
      try {
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        setStoredValue(valueToStore);
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
        window.dispatchEvent(new CustomEvent('local-storage-updated', { detail: { key } }));
      } catch (error) {
        console.warn(`Error setting localStorage key “${key}”:`, error);
      }
    },
    [key, storedValue] // storedValue is needed here for the function variant of `value`
  );

  useEffect(() => {
    if (typeof window === 'undefined') {
      return;
    }

    const handleStorageChange = (event: Event) => {
      let eventKey: string | null = null;
      let needsSync = false;

      if (event instanceof StorageEvent) {
        eventKey = event.key;
        // Sync if the key matches or if localStorage was cleared (event.key is null)
        if (eventKey === key || (eventKey === null && event.storageArea === window.localStorage)) {
            needsSync = true;
        }
      } else if (event.type === 'local-storage-updated') {
        const customEvent = event as CustomEvent<{ key?: string }>;
        eventKey = customEvent.detail?.key || null;
         // Sync if the key matches or if the custom event is a global signal (no key)
        if (eventKey === key || !eventKey) { 
            needsSync = true;
        }
      }

      if (needsSync) {
        // Use the latest initialValue from the ref as a fallback
        const actualInitialValueForRefresh =
            typeof initialValueRef.current === 'function'
            ? (initialValueRef.current as () => T)()
            : initialValueRef.current;
        try {
            const item = window.localStorage.getItem(key);
            const newValue = item ? safeJsonParse<T>(item, actualInitialValueForRefresh) : actualInitialValueForRefresh;
            
            // Only update state if the value from storage is different from the current state
            // This comparison helps prevent loops if multiple events fire or if parsing/stringifying
            // results in referentially different but semantically identical objects.
            setStoredValue(currentStoredValue => {
              if (JSON.stringify(currentStoredValue) !== JSON.stringify(newValue)) {
                return newValue;
              }
              return currentStoredValue;
            });
        } catch (error) {
            console.warn(`Error reading localStorage key "${key}" during sync:`, error);
            // Fallback to initial value in case of error during sync read
            setStoredValue(actualInitialValueForRefresh);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    window.addEventListener('local-storage-updated', handleStorageChange);

    return () => {
      window.removeEventListener('storage', handleStorageChange);
      window.removeEventListener('local-storage-updated', handleStorageChange);
    };
  }, [key]); // This effect now only depends on `key`. `initialValueRef` is stable.

  return [storedValue, setValue];
}
