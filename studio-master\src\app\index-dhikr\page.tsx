
"use client";

import { DhikrCard } from "@/components/dhikr/dhikr-card";
import { getDhikrByCategory } from "@/lib/dhikrData";
import type { Dhikr } from "@/types/dhikr";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { ScrollArea } from "@/components/ui/scroll-area";
import Image from "next/image";
import { motion } from "framer-motion";
import { listContainerVariants } from "@/lib/animations";

export default function DhikrIndexPage() {
  const categorizedDhikr: Record<string, Dhikr[]> = getDhikrByCategory();
  const categories = Object.keys(categorizedDhikr);

  return (
    <div className="space-y-8">
      <header className="text-center space-y-4 py-8 bg-gradient-to-b from-background to-secondary/30 rounded-xl shadow-sm">
         <Image 
          src="https://picsum.photos/1200/250" 
          alt="Islamic Pattern Banner" 
          width={1200} 
          height={250} 
          className="w-full h-40 object-cover rounded-lg shadow-md"
          data-ai-hint="Islamic pattern"
        />
        <h1 className="text-4xl md:text-5xl font-bold tracking-tight text-primary font-arabic">فهرس الأذكار</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          تصفح جميع الأذكار المتاحة، مصنفة لتسهيل وصولك إليها في أي وقت.
        </p>
      </header>

      {categories.length > 0 ? (
        <Accordion type="multiple" className="w-full space-y-4">
          {categories.map((category) => (
            <AccordionItem key={category} value={category} className="bg-card border-none rounded-lg shadow-md overflow-hidden">
              <AccordionTrigger className="px-6 py-4 text-xl font-semibold hover:bg-muted/50 transition-colors font-arabic text-primary">
                {category} ({categorizedDhikr[category].length})
              </AccordionTrigger>
              <AccordionContent className="px-2 sm:px-4 md:px-6 pt-0 pb-4">
                <ScrollArea className="max-h-[600px] pr-3">
                  <motion.div 
                    className="space-y-4"
                    variants={listContainerVariants}
                    initial="hidden"
                    animate="visible" // This will re-animate when AccordionContent becomes visible
                  >
                    {categorizedDhikr[category].map((dhikr) => (
                      <DhikrCard key={dhikr.id} dhikr={dhikr} />
                    ))}
                  </motion.div>
                </ScrollArea>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      ) : (
         <div className="text-center py-12">
          <Image 
            src="https://picsum.photos/300/200" 
            alt="Empty state bookshelf" 
            width={300} 
            height={200} 
            className="mx-auto rounded-lg mb-4 shadow"
            data-ai-hint="empty bookshelf"
          />
          <p className="text-xl text-muted-foreground">فهرس الأذكار فارغ حالياً.</p>
        </div>
      )}
    </div>
  );
}
