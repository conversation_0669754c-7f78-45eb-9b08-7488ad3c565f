{"version": 3, "sources": ["../src/index.ts", "../src/portal.tsx"], "sourcesContent": ["'use client';\nexport {\n  Portal,\n  //\n  Root,\n} from './portal';\nexport type { PortalProps } from './portal';\n", "import * as React from 'react';\nimport ReactDOM from 'react-dom';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useLayoutEffect } from '@radix-ui/react-use-layout-effect';\n\n/* -------------------------------------------------------------------------------------------------\n * Portal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'Portal';\n\ntype PortalElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface PortalProps extends PrimitiveDivProps {\n  /**\n   * An optional container where the portaled content should be appended.\n   */\n  container?: Element | DocumentFragment | null;\n}\n\nconst Portal = React.forwardRef<PortalElement, PortalProps>((props, forwardedRef) => {\n  const { container: containerProp, ...portalProps } = props;\n  const [mounted, setMounted] = React.useState(false);\n  useLayoutEffect(() => setMounted(true), []);\n  const container = containerProp || (mounted && globalThis?.document?.body);\n  return container\n    ? ReactDOM.createPortal(<Primitive.div {...portalProps} ref={forwardedRef} />, container)\n    : null;\n});\n\nPortal.displayName = PORTAL_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = Portal;\n\nexport {\n  Portal,\n  //\n  Root,\n};\nexport type { PortalProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,YAAuB;AACvB,uBAAqB;AACrB,6BAA0B;AAC1B,qCAAgC;AAuBJ;AAjB5B,IAAM,cAAc;AAWpB,IAAM,SAAe,iBAAuC,CAAC,OAAO,iBAAiB;AACnF,QAAM,EAAE,WAAW,eAAe,GAAG,YAAY,IAAI;AACrD,QAAM,CAAC,SAAS,UAAU,IAAU,eAAS,KAAK;AAClD,sDAAgB,MAAM,WAAW,IAAI,GAAG,CAAC,CAAC;AAC1C,QAAM,YAAY,iBAAkB,WAAW,YAAY,UAAU;AACrE,SAAO,YACH,iBAAAA,QAAS,aAAa,4CAAC,iCAAU,KAAV,EAAe,GAAG,aAAa,KAAK,cAAc,GAAI,SAAS,IACtF;AACN,CAAC;AAED,OAAO,cAAc;AAIrB,IAAM,OAAO;", "names": ["ReactDOM"]}