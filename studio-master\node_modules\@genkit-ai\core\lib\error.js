"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var error_exports = {};
__export(error_exports, {
  GenkitError: () => GenkitError,
  UnstableApiError: () => UnstableApiError,
  UserFacingError: () => UserFacingError,
  assertUnstable: () => assertUnstable,
  getCallableJSON: () => getCallableJSON,
  getErrorMessage: () => getErrorMessage,
  getErrorStack: () => getErrorStack,
  getHttpStatus: () => getHttpStatus
});
module.exports = __toCommonJS(error_exports);
var import_statusTypes = require("./statusTypes.js");
class GenkitError extends Error {
  source;
  status;
  detail;
  code;
  // For easy printing, we wrap the error with information like the source
  // and status, but that's redundant with JSON.
  originalMessage;
  constructor({
    status,
    message,
    detail,
    source
  }) {
    super(`${source ? `${source}: ` : ""}${status}: ${message}`);
    this.originalMessage = message;
    this.code = (0, import_statusTypes.httpStatusCode)(status);
    this.status = status;
    this.detail = detail;
    this.name = "GenkitError";
  }
  /**
   * Returns a JSON-serializable representation of this object.
   */
  toJSON() {
    return {
      // This error type is used by 3P authors with the field "detail",
      // but the actual Callable protocol value is "details"
      ...this.detail === void 0 ? {} : { details: this.detail },
      status: this.status,
      message: this.originalMessage
    };
  }
}
class UnstableApiError extends GenkitError {
  constructor(level, message) {
    super({
      status: "FAILED_PRECONDITION",
      message: `${message ? message + " " : ""}This API requires '${level}' stability level.

To use this feature, initialize Genkit using \`import {genkit} from "genkit/${level}"\`.`
    });
    this.name = "UnstableApiError";
  }
}
function assertUnstable(registry, level, message) {
  if (level === "beta" && registry.apiStability === "stable") {
    throw new UnstableApiError(level, message);
  }
}
class UserFacingError extends GenkitError {
  constructor(status, message, details) {
    super({ status, detail: details, message });
    super.name = "UserFacingError";
  }
}
function getHttpStatus(e) {
  if (e instanceof GenkitError) {
    return e.code;
  }
  return 500;
}
function getCallableJSON(e) {
  if (e instanceof GenkitError) {
    return e.toJSON();
  }
  return {
    message: "Internal Error",
    status: "INTERNAL"
  };
}
function getErrorMessage(e) {
  if (e instanceof Error) {
    return e.message;
  }
  return `${e}`;
}
function getErrorStack(e) {
  if (e instanceof Error) {
    return e.stack;
  }
  return void 0;
}
// Annotate the CommonJS export names for ESM import in node:
0 && (module.exports = {
  GenkitError,
  UnstableApiError,
  UserFacingError,
  assertUnstable,
  getCallableJSON,
  getErrorMessage,
  getErrorStack,
  getHttpStatus
});
//# sourceMappingURL=error.js.map