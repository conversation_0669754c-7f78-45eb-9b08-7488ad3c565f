
"use client";

import type { <PERSON><PERSON><PERSON> } from '@/types/reminder';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Trash2, <PERSON><PERSON><PERSON>, BellR<PERSON> } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Image from "next/image";

interface ReminderListProps {
  reminders: <PERSON>mind<PERSON>[];
  onToggleReminder: (id: string) => void;
  onDeleteReminder: (id: string) => void;
}

export function ReminderList({ reminders, onToggleReminder, onDeleteReminder }: ReminderListProps) {
  if (reminders.length === 0) {
    return (
      <div className="text-center py-10 bg-card rounded-lg shadow-sm">
        <Image 
          src="https://picsum.photos/200/150" 
          alt="No reminders illustration" 
          width={200} 
          height={150}
          className="mx-auto rounded-lg mb-6 shadow"
          data-ai-hint="empty calendar"
        />
        <p className="text-xl text-muted-foreground">لا توجد تذكيرات حالياً.</p>
        <p className="text-sm text-muted-foreground mt-2">قم بإضافة تذكير جديد لتبدأ!</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {reminders.map(reminder => (
        <Card key={reminder.id} className={`transition-opacity duration-300 ${reminder.isActive ? 'opacity-100' : 'opacity-60 bg-muted/30'}`}>
          <CardHeader className="flex flex-row items-center justify-between pb-3">
            <div className="space-y-1">
              <CardTitle className="text-xl font-arabic leading-snug">{reminder.dhikrArabicText.substring(0, 50)}{reminder.dhikrArabicText.length > 50 ? '...' : ''}</CardTitle>
              <CardDescription className="text-lg font-mono text-primary">{reminder.time}</CardDescription>
            </div>
            <Switch
              checked={reminder.isActive}
              onCheckedChange={() => onToggleReminder(reminder.id)}
              aria-label={reminder.isActive ? "تعطيل التذكير" : "تفعيل التذكير"}
              className="data-[state=checked]:bg-primary data-[state=unchecked]:bg-muted-foreground/50"
            />
          </CardHeader>
          <CardContent className="flex items-center justify-between pt-2 border-t">
             <Badge variant={reminder.isActive ? "default" : "outline"} className="flex items-center gap-1.5">
              {reminder.isActive ? <BellRing className="w-4 h-4" /> : <BellOff className="w-4 h-4" />}
              {reminder.isActive ? 'مفعّل' : 'معطّل'}
            </Badge>
            <Button variant="ghost" size="icon" onClick={() => onDeleteReminder(reminder.id)} aria-label="حذف التذكير" className="text-destructive hover:bg-destructive/10 rounded-full">
              <Trash2 className="w-5 h-5" />
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
