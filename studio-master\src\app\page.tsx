
"use client";

import { useState, useEffect } from 'react';
import { DhikrCard } from "@/components/dhikr/dhikr-card";
import { getDailyDhikr } from "@/lib/dhikrData";
import type { Dhikr } from "@/types/dhikr";
import { Separator } from "@/components/ui/separator";
import Image from "next/image";
import { motion } from "framer-motion";
import { listContainerVariants } from "@/lib/animations";

export default function HomePage() {
  const [dailyDhikrList, setDailyDhikrList] = useState<Dhikr[]>([]);

  useEffect(() => {
    // Ensure this runs only on the client
    setDailyDhikrList(getDailyDhikr(5));
  }, []);

  return (
    <div className="space-y-8">
      <header className="text-center space-y-4 py-8 bg-gradient-to-b from-background to-secondary/30 rounded-xl shadow-sm">
        <Image
          src="https://picsum.photos/1200/300"
          alt="Islamic Calligraphy Banner"
          width={1200}
          height={300}
          className="w-full h-48 object-cover rounded-lg shadow-md"
          data-ai-hint="Islamic calligraphy"
          priority
        />
        <h1 className="text-4xl md:text-5xl font-bold tracking-tight text-primary font-arabic">الأذكار اليومية</h1>
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          ابدأ يومك بذكر الله والصلاة على النبي صلى الله عليه وسلم. إليك مجموعة مختارة من الأذكار لتنير يومك.
        </p>
      </header>

      <Separator />

      {dailyDhikrList.length > 0 ? (
        <motion.div
          className="grid gap-6 md:gap-8"
          variants={listContainerVariants}
          initial="hidden"
          animate="visible"
        >
          {dailyDhikrList.map((dhikr) => (
            <DhikrCard key={dhikr.id} dhikr={dhikr} />
          ))}
        </motion.div>
      ) : (
        <div className="text-center py-12">
          <Image
            src="https://picsum.photos/300/200"
            alt="Empty state"
            width={300}
            height={200}
            className="mx-auto rounded-lg mb-4 shadow"
            data-ai-hint="loading progress"
          />
          <p className="text-xl text-muted-foreground">جاري تحميل الأذكار اليومية...</p>
        </div>
      )}
    </div>
  );
}
