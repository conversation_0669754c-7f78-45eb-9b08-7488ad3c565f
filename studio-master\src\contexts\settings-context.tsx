
"use client";

import type { ReactNode } from 'react';
import { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { useLocalStorage } from '@/hooks/use-local-storage';

export type ArabicFont = 'default' | 'noto-naskh' | 'amiri' | 'scheherazade';

interface Settings {
  arabicFont: ArabicFont;
}

interface SettingsContextType {
  settings: Settings;
  updateSetting: <K extends keyof Settings>(key: K, value: Settings[K]) => void;
  availableFonts: { value: ArabicFont, label: string }[];
}

const defaultSettings: Settings = {
  arabicFont: 'default',
};

const availableFonts: { value: ArabicFont, label: string }[] = [
  { value: 'default', label: 'افتراضي (Times New Roman)' },
  { value: 'noto-naskh', label: 'Noto Naskh Arabic' },
  { value: 'amiri', label: '<PERSON><PERSON>' },
  { value: 'scheherazade', label: 'Scheherazade New' },
];

const SettingsContext = createContext<SettingsContextType | undefined>(undefined);

export const SettingsProvider = ({ children }: { children: ReactNode }) => {
  const [storedSettings, setStoredSettings] = useLocalStorage<Settings>('app_settings', defaultSettings);
  const [settings, setSettings] = useState<Settings>(storedSettings);

  // Update internal state if localStorage changes from another tab/window
  useEffect(() => {
    setSettings(storedSettings);
  }, [storedSettings]);
  
  const updateSetting = useCallback(<K extends keyof Settings>(key: K, value: Settings[K]) => {
    setStoredSettings(prev => {
      const newSettings = { ...prev, [key]: value };
      setSettings(newSettings); // Also update local state immediately for current tab responsiveness
      return newSettings;
    });
  }, [setStoredSettings]);

  return (
    <SettingsContext.Provider value={{ settings, updateSetting, availableFonts }}>
      {children}
    </SettingsContext.Provider>
  );
};

export const useSettings = (): SettingsContextType => {
  const context = useContext(SettingsContext);
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider');
  }
  return context;
};
