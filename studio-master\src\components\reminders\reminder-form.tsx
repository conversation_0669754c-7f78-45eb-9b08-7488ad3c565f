"use client";

import { useState } from 'react';
import { useForm, type SubmitHandler } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ALL_DHIKR } from '@/lib/dhikrData';
import type { Reminder } from '@/types/reminder';
import { useToast } from "@/hooks/use-toast";
import { PlusCircle } from 'lucide-react';

const reminderSchema = z.object({
  dhikrId: z.string().min(1, "الرجاء اختيار الذكر"),
  time: z.string().regex(/^([01]\d|2[0-3]):([0-5]\d)$/, "الرجاء إدخال وقت صحيح (HH:MM)"),
  // frequency: z.enum(['daily', 'once']).default('daily'), // For future expansion
});

type ReminderFormData = z.infer<typeof reminderSchema>;

interface ReminderFormProps {
  onAddReminder: (reminder: Reminder) => void;
}

export function ReminderForm({ onAddReminder }: ReminderFormProps) {
  const { toast } = useToast();
  const form = useForm<ReminderFormData>({
    resolver: zodResolver(reminderSchema),
    defaultValues: {
      dhikrId: '',
      time: '',
    },
  });

  const onSubmit: SubmitHandler<ReminderFormData> = (data) => {
    const selectedDhikr = ALL_DHIKR.find(d => d.id === data.dhikrId);
    if (!selectedDhikr) {
      toast({ title: "خطأ", description: "الذكر المختار غير موجود.", variant: "destructive" });
      return;
    }

    const newReminder: Reminder = {
      id: crypto.randomUUID(),
      dhikrId: data.dhikrId,
      dhikrArabicText: selectedDhikr.arabicText, // Store text for display
      time: data.time,
      isActive: true,
    };
    onAddReminder(newReminder);
    toast({ title: "تم إضافة التذكير", description: `سيتم تذكيرك بـ "${selectedDhikr.arabicText.substring(0,20)}..." في الساعة ${data.time}.` });
    form.reset();
  };

  return (
    <Card className="w-full shadow-lg">
      <CardHeader>
        <CardTitle className="text-2xl font-arabic flex items-center gap-2">
          <PlusCircle className="w-6 h-6 text-primary" />
          إضافة تذكير جديد
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="dhikrId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>الذكر</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="اختر الذكر" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {ALL_DHIKR.map(dhikr => (
                        <SelectItem key={dhikr.id} value={dhikr.id} className="font-arabic text-right">
                          {dhikr.arabicText.length > 50 ? dhikr.arabicText.substring(0, 50) + "..." : dhikr.arabicText}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="time"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>الوقت (24 ساعة)</FormLabel>
                  <FormControl>
                    <Input type="time" {...field} className="text-lg text-left" dir="ltr" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" className="w-full text-lg py-3">
              <PlusCircle className="rtl:ml-2 ltr:mr-2 h-5 w-5" />
              إضافة التذكير
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
