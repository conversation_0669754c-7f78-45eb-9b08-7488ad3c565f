{"name": "deeks", "version": "3.1.0", "description": "Retrieve all keys and nested keys from objects and arrays of objects.", "main": "lib/deeks.js", "types": "lib/deeks.d.ts", "scripts": {"build": "npm run lint && npm run test && npm run compile", "compile": "tsc -p tsconfig.build.json", "coverage": "nyc npm run test", "lint": "eslint --ext .js,.ts src test", "prepublishOnly": "npm run build", "test": "mocha -r ts-node/register test/index.ts"}, "repository": {"type": "git", "url": "git+https://github.com/mrodrig/deeks.git"}, "keywords": ["get", "keys", "object", "document", "deep", "deeks", "recursive"], "author": "m<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/mrodrig/deeks/issues"}, "homepage": "https://mrodrig.github.io/deeks", "dependencies": {}, "devDependencies": {"@types/mocha": "10.0.1", "@types/node": "18.15.3", "@typescript-eslint/eslint-plugin": "5.55.0", "@typescript-eslint/parser": "5.55.0", "eslint": "8.36.0", "eslint-config-google": "0.14.0", "eslint-plugin-import": "2.27.5", "mocha": "10.2.0", "nyc": "15.1.0", "ts-node": "10.9.1", "typescript": "5.0.2"}, "engines": {"node": ">= 16"}}