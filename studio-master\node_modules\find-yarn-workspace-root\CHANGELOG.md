# Change Log

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.

<a name="2.0.0"></a>
# [2.0.0](https://github.com/square/find-yarn-workspace-root/compare/v1.2.1...v2.0.0) (2020-06-11)


### Chores

* **deps:** update micromatch to v4 ([c7e8189](https://github.com/square/find-yarn-workspace-root/commit/c7e8189))


### BREAKING CHANGES

* **deps:** requires Node >= 8.6



<a name="1.2.1"></a>
## [1.2.1](https://github.com/square/find-yarn-workspace-root/compare/v1.2.0...v1.2.1) (2018-08-30)


### Bug Fixes

* add typescript declaration ([7c9c96f](https://github.com/square/find-yarn-workspace-root/commit/7c9c96f))



<a name="1.2.0"></a>
# [1.2.0](https://github.com/square/find-yarn-workspace-root/compare/v1.0.0...v1.2.0) (2018-08-09)


### Features

* allow new `manifest.workspaces.packages` format ([5521d97](https://github.com/square/find-yarn-workspace-root/commit/5521d97))
* make initial path optional ([475aaff](https://github.com/square/find-yarn-workspace-root/commit/475aaff))



<a name="1.1.0"></a>
# [1.1.0](https://github.com/square/find-yarn-workspace-root/compare/v1.0.0...v1.1.0) (2018-03-23)


### Features

* allow new `manifest.workspaces.packages` format ([5521d97](https://github.com/square/find-yarn-workspace-root/commit/5521d97))



# Change Log

All notable changes to this project will be documented in this file. See [standard-version](https://github.com/conventional-changelog/standard-version) for commit guidelines.
