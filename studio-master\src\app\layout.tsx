
import type { <PERSON>ada<PERSON> } from 'next';
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from 'next/font/google';
import './globals.css';
import { AppShell } from '@/components/layout/app-shell';
import { Toaster } from "@/components/ui/toaster";
import { SettingsProvider } from '@/contexts/settings-context';
import { ApplyGlobalSettings } from '@/components/layout/apply-global-settings';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'رفيق الذكر - تطبيق الأذكار اليومية',
  description: 'تطبيقك اليومي للأذكار والتسابيح والمزيد.',
};

export default function RootLayout({
  children,
}: <PERSON>only<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="ar" dir="rtl">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <SettingsProvider>
          <ApplyGlobalSettings />
          <AppShell>
            {children}
          </AppShell>
          <Toaster />
        </SettingsProvider>
      </body>
    </html>
  );
}
